import os
import FEAPI_Request as r
import FEAPI_Endpoints as e

#training Eureka user authnetication
my_api = 'https://feapi.callminer.net/api/v2/'
my_sec = 'https://sapicm.callminer.net/'
tenant = "training02"

# retrieve username and pwd from USer Account environemtn variable
username = os.getenv("FEAPI_username")
password = os.getenv("FEAPI_pwd")

# call API
api_request = r.FEAPIRequest(my_api, my_sec, username, password, tenant)

api_response = api_request.make_post_request("search/validateuserentry","\"\\\"test|hello query\\\":1.5\"")

print (f"api_response = {api_response}")

# call Endpoint
api_endpoint = e.FEAPI_Endpoints(my_api, my_sec, username, password, tenant)

api_response = api_endpoint.ValidateUserEntry('"test|hello query":1.5')

print (f"api_endpoint = {api_response}")

