# CallMiner FEAPI Python Client - documented with Augment

A Python client library for interacting with the CallMiner Eureka Frontend API (FEAPI). This library provides a simple interface for authenticating with the CallMiner security API and making requests to various FEAPI endpoints.

# Getting Started
## Installation process
1. Clone this repository
2. Install required dependencies: `pip install requests`

## Configuration
Set the following environment variables:
- `FEAPI_username`: Your CallMiner username
- `FEAPI_pwd`: Your CallMiner password

## Usage Example
```python
import os
from FEAPI_Endpoints import FEAPI_Endpoints

# Configure API endpoints
api_url = 'https://feapidevatl.callminer.net/api/v2/'
security_url = 'https://sapidevatl.callminer.net/'
username = os.getenv("FEAPI_username")
password = os.getenv("FEAPI_pwd")
tenant = "devatl"

# Initialize the client
client = FEAPI_Endpoints(api_url, security_url, username, password, tenant)

# Make API calls
response = client.ValidateUserEntry("'hello'")
print(response)
```

# Features
- JWT token-based authentication
- Automatic token refresh handling
- Support for GET, POST, PUT, and DELETE requests
- Error handling for API responses

# Build and Test
Run the example code in either `FEAPI_Request.py` or `FEAPI_Endpoints.py` to test the basic functionality.

# Contribute
Contributions are welcome! Please feel free to submit a Pull Request.
