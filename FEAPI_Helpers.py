import os
import csv
import pandas as pd
from typing import List, Dict, Any, Union, Optional
from FEAPI_Endpoints import FEAPI_Endpoints
import shutil
from openpyxl import load_workbook

class FEAPI_Helpers:
    """
    Helper class for CallMiner FEAPI operations.
    Stores API connection information and provides utility functions.
    """
    
    def __init__(self, 
                api_address: str = None, 
                security_address: str = None, 
                username: str = None, 
                password: str = None, 
                tenant: str = None):
        """
        Initialize the FEAPI_Helpers with connection information.
        
        Args:
            api_address: The FEAPI endpoint URL
            security_address: The security API URL
            username: CallMiner username
            password: CallMiner password
            tenant: CallMiner tenant name
        """
        # Use provided values or defaults
        self.api_address = api_address or 'https://feapi.callminer.net/api/v2/'
        self.security_address = security_address or 'https://sapicm.callminer.net/'
        self.username = username or os.getenv("FEAPI_username")
        self.password = password or os.getenv("FEAPI_pwd")
        self.tenant = tenant or "training02"
        
        # Initialize the FEAPI client
        self.endpoints = FEAPI_Endpoints(
            self.api_address, 
            self.security_address, 
            self.username, 
            self.password, 
            self.tenant
        )
    
    def process_user_entries(self, user_entries: List[str]) -> Dict[str, Any]:
        """
        Process a list of user entries and return results as a dictionary.
        
        Args:
            user_entries: List of user entry strings to validate
            
        Returns:
            Dictionary with user entries as keys and validation results as values
        """
        results = {}
        
        for entry in user_entries:
            try:
                # Call the ValidateUserEntry method for each entry
                response = self.endpoints.ValidateUserEntry(entry)
                results[entry] = response
            except Exception as e:
                # Store the error message if validation fails
                results[entry] = f"Error: {str(e)}"
        
        return results

    def process_csv_file(self, 
                        input_file_path: str, 
                        user_entry_column: str = "userentry",
                        output_column: str = "validation_result") -> str:
        """
        Process user entries from a CSV file and save results to a new CSV file.
        
        Args:
            input_file_path: Path to the input CSV file
            user_entry_column: Name of the column containing user entries to validate
            output_column: Name of the column to store validation results
        
        Returns:
            Path to the output CSV file
        
        Raises:
            FileNotFoundError: If the input file doesn't exist
            ValueError: If the specified column doesn't exist in the CSV
        """
        # Check if file exists
        if not os.path.exists(input_file_path):
            raise FileNotFoundError(f"Input file not found: {input_file_path}")
        
        # Generate output file path
        output_file_path = f"{os.path.splitext(input_file_path)[0]}.output.csv"
        
        # Read the CSV file
        try:
            df = pd.read_csv(input_file_path, quoting=csv.QUOTE_NONE)
        except Exception as e:
            raise ValueError(f"Error reading CSV file: {str(e)}")
        
        # Check if the column exists
        if user_entry_column not in df.columns:
            raise ValueError(f"Column '{user_entry_column}' not found in the CSV file")
        

        # Create a new column for results using the mapping
        validation_results = []

        for entry in df[user_entry_column].astype(str):
            response = ""
            # Wrap the entry in single quotes for the API
            try:
                # Call the ValidateUserEntry method for each entry
                response = self.endpoints.ValidateUserEntry(entry)
            except Exception as e:
                # Store the error message if validation fails
                response = f"Error: {str(e)}"
        
            validation_results.append(response)

        
        df[output_column] = validation_results
        
        # Save to output file
        df.to_csv(output_file_path, index=True)
        
        return output_file_path
    
    def process_excel_file(self,
                        xlsx_file_name: str,
                        sheet_name: str,
                        user_entry_column: str) -> str:
        """
        Process user entries from an Excel (.xlsx) file and save results to a new Excel file.
        
        Args:
            xlsx_file_name: Path to the input Excel file (.xlsx)
            sheet_name: Name of the sheet to process
            user_entry_column: Name of the column containing user entries to validate
        
        Returns:
            Path to the output Excel file (_output.xlsx)
        
        Raises:
            FileNotFoundError: If the input file doesn't exist
            ValueError: If the specified sheet or column doesn't exist
        """

        if not os.path.exists(xlsx_file_name):
            raise FileNotFoundError(f"Input Excel file not found: {xlsx_file_name}")

        # Output filename with '.output.xlsx' suffix
        base_name, ext = os.path.splitext(xlsx_file_name)
        output_file_path = f"{base_name}_output.xlsx"

        # Copy the input file to output file
        shutil.copy2(xlsx_file_name, output_file_path)

        # Load workbook & sheet using openpyxl
        wb = load_workbook(output_file_path)
        if sheet_name not in wb.sheetnames:
            raise ValueError(f"Sheet '{sheet_name}' not found in workbook")
        
        ws = wb[sheet_name]

        # Identify the user_entry_column index by reading the header row (assumed at row 1)
        user_entry_col_idx = None
        max_col = ws.max_column

        for col in range(1, max_col + 1):
            cell_val = ws.cell(row=1, column=col).value
            if cell_val == user_entry_column:
                user_entry_col_idx = col
                break

        if user_entry_col_idx is None:
            raise ValueError(f"Column '{user_entry_column}' not found in sheet '{sheet_name}'")

        # Determine the new column index for 'action_string' (append to the right)
        action_string_col_idx = max_col + 1
        ws.cell(row=1, column=action_string_col_idx, value="action_string")

        # Iterate over rows (starting from row 2, assuming row 1 is header)
        for row in range(2, ws.max_row + 1):
            user_entry_cell = ws.cell(row=row, column=user_entry_col_idx)
            user_entry = str(user_entry_cell.value) if user_entry_cell.value is not None else ""

            try:
                action_string = self.endpoints.ValidateUserEntry(user_entry)
            except Exception as e:
                action_string = f"Error: {str(e)}"

            ws.cell(row=row, column=action_string_col_idx, value=action_string)

        # Save the workbook
        wb.save(output_file_path)

        return output_file_path

if __name__ == '__main__':
    # Example usage
    helpers = FEAPI_Helpers()

    # Example 1: Process a list of user entries
    print("Example 1: Processing a list of user entries")
    test_entries = ["hello", "\"hello world\"", "\"test|hello query\""]
    results = helpers.process_user_entries(test_entries) 
    
    for entry, result in results.items():
        print(f"Entry: {entry}")
        print(f"Result: {result}")
        print("-" * 50)
    
    # Example 2: Process a CSV file from testdata folder
    print("\nExample 2: Processing a CSV file from testdata folder")
    try:
        # Use the existing CSV file in the testdata folder
        test_csv_path = "testdata/test.userentry.csv"
        
        print(f"Using CSV file: {test_csv_path}")
        
        # Process the CSV file
        output_path = helpers.process_csv_file(test_csv_path, "userentry")
        print(f"Processed CSV saved to: {output_path}")
        
        # Display the results
        result_df = pd.read_csv(output_path)
        print("\nResults (first 5 rows):")
        print(result_df.head(5))
        
    except Exception as e:
        print(f"Error in CSV processing example: {str(e)}")

    # Example 3
    # Assuming your test Excel file is .xlsx formatted
    try:
        excel_file = "testdata/test2_userentry.xlsx"
        sheet = "Sheet1"
        user_col = "User Entry"

        print(f"\nProcessing Excel file '{excel_file}', sheet '{sheet}', column '{user_col}'")

        output_excel = helpers.process_excel_file(excel_file, sheet, user_col)
        print(f"Processed Excel saved to: {output_excel}")

    except Exception as e:
        print(f"Error processing Excel file: {e}")
    






