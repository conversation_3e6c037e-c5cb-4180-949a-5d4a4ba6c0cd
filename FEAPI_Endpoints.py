import os
import FEAPI_Request as r


class FEAPIException(Exception):
    """Custom exception for FEAPI errors with detailed error information"""
    def __init__(self, message, status_code=None, endpoint=None, method=None):
        self.message = message
        self.status_code = status_code
        self.endpoint = endpoint
        self.method = method
        super().__init__(self.message)

    def __str__(self):
        error_details = f"FEAPI Error: {self.message}"
        if self.status_code:
            error_details += f" (Status Code: {self.status_code})"
        if self.endpoint:
            error_details += f" (Endpoint: {self.endpoint})"
        if self.method:
            error_details += f" (Method: {self.method})"
        return error_details


class FEAPI_Endpoints:

    def __init__(self, api_address, security_address, username, passw, tenant, token=''):
        self.feapi_request = r.FEAPIRequest(api_address, security_address, username, passw, tenant)

    def _handle_api_error(self, error, endpoint, method):
        """
        Handle API errors and provide detailed error explanations

        Args:
            error: The original ValueError from FEAPI_Request
            endpoint: The API endpoint that was called
            method: The HTTP method used

        Raises:
            FEAPIException: Custom exception with detailed error information
        """
        error_message = str(error)
        status_code = None

        # Extract status code from error message if present
        if "Status code:" in error_message:
            try:
                # Extract status code from the error message
                status_part = error_message.split("Status code:")[1].split("Message")[0].strip()
                status_code = int(status_part)
            except (IndexError, ValueError):
                pass

        # Provide detailed explanations for common error codes
        if status_code == 400:
            explanation = ("Bad Request - The request was invalid. This could be due to: "
                         "missing required parameters, invalid parameter values, "
                         "malformed JSON data, or invalid query parameters.")
        elif status_code == 401:
            explanation = ("Unauthorized - Authentication failed. This could be due to: "
                         "invalid credentials, expired token, insufficient permissions, "
                         "or missing authentication headers.")
        elif status_code == 403:
            explanation = ("Forbidden - Access denied. You don't have permission to access this resource. "
                         "Check your user permissions and group memberships.")
        elif status_code == 404:
            explanation = ("Not Found - The requested resource was not found. "
                         "Check if the ID exists and you have access to it.")
        elif status_code == 500:
            explanation = ("Internal Server Error - An error occurred on the server. "
                         "This is typically a temporary issue. Try again later.")
        else:
            explanation = f"An API error occurred: {error_message}"

        raise FEAPIException(explanation, status_code, endpoint, method)

    def FormatUserEntry(self, userentry: str) -> str:
        """
        Formats a user entry string by escaping existing double quotes and
        enclosing the entire string in double quotes.

        Args:
            userentry: The input string from the user.

        Returns:
            The formatted string with escaped double quotes and
            enclosed in double quotes.
        """
        # Step 1: Escape any double quotes already present in the userentry string
        # Replace each " with \"
        escaped_entry = userentry.replace('"', '\\"')

        # Step 2: Add double quotes around the entire escaped string
        # This creates the final desired format: "original string with escaped quotes"
        formatted_string = f'"{escaped_entry}"'

        return formatted_string

    def ValidateUserEntry(self, userentry):
        """
        Validates a user entry string using the search API

        Args:
            userentry: The input string from the user to validate

        Returns:
            API response containing validation results

        Raises:
            FEAPIException: If the API call fails with detailed error information
        """
        try:
            userentryformatted = self.FormatUserEntry(userentry)
            api_response = self.feapi_request.make_post_request("search/validateuserentry", userentryformatted)
            return api_response
        except ValueError as e:
            self._handle_api_error(e, "search/validateuserentry", "POST")

    # Categories Controller Endpoints

    def GetAllCategories(self, public_only=None):
        """
        Gets all categories

        Args:
            public_only: Indicates if only public categories should be returned in the response.
                        Public categories are neither associated to a group nor associated to an individual user.

        Returns:
            API response containing all categories

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid publicOnly parameter value
                - 401: Unauthorized - Authentication failed or insufficient permissions
        """
        try:
            endpoint = "categories"
            if public_only is not None:
                endpoint += f"?publicOnly={str(public_only).lower()}"

            api_response = self.feapi_request.make_get_request(endpoint)
            return api_response
        except ValueError as e:
            self._handle_api_error(e, endpoint, "GET")

    def UpdateCategory(self, category_data):
        """
        Updates an existing category

        Args:
            category_data: The category object in JSON format

        Returns:
            API response containing the updated category ID

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid category data, missing BucketId, or validation errors
                - 401: Unauthorized - Authentication failed or insufficient permissions to edit categories
        """
        try:
            api_response = self.feapi_request.make_put_request("categories", category_data)
            return api_response
        except ValueError as e:
            self._handle_api_error(e, "categories", "PUT")

    def CreateCategory(self, category_data):
        """
        Creates a new category

        Args:
            category_data: The request object used to create a category

        Returns:
            API response containing the new category ID

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid category data, duplicate name, or validation errors
                - 401: Unauthorized - Authentication failed or insufficient permissions to create categories
        """
        try:
            api_response = self.feapi_request.make_post_request("categories", category_data)
            return api_response
        except ValueError as e:
            self._handle_api_error(e, "categories", "POST")

    def GetRetroMaxDays(self):
        """
        Get Retro Max Days
        Fetches the maximum number of days allowed for retroprocessing

        Returns:
            API response containing the maximum number of days

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid request format
                - 401: Unauthorized - Authentication failed or insufficient permissions
        """
        try:
            api_response = self.feapi_request.make_get_request("categories/retromaxdays")
            return api_response
        except ValueError as e:
            self._handle_api_error(e, "categories/retromaxdays", "GET")

    def UpdateCategoryRetroDate(self, update_request):
        """
        Updates the retroactive date of a category

        Args:
            update_request: The request to update the retroactive date for a category

        Returns:
            API response

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid request data, missing category ID, or invalid date format
                - 401: Unauthorized - Authentication failed or insufficient permissions to update categories
        """
        try:
            api_response = self.feapi_request.make_post_request("categories/updateCategoryRetroDate", update_request)
            return api_response
        except ValueError as e:
            self._handle_api_error(e, "categories/updateCategoryRetroDate", "POST")

    def GetCategoryComponentsByCategoryId(self, category_id):
        """
        Gets category components by Category ID

        Args:
            category_id: The unique identifier of the category that the components are associated with

        Returns:
            API response containing the category components

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid category ID format
                - 401: Unauthorized - Authentication failed or insufficient permissions
                - 404: Not Found - Category with the specified ID does not exist
        """
        try:
            api_response = self.feapi_request.make_get_request(f"categories/{category_id}/components")
            return api_response
        except ValueError as e:
            self._handle_api_error(e, f"categories/{category_id}/components", "GET")

    def GetCategoryByCategoryId(self, category_id, components=None):
        """
        Gets a single category by Category ID

        Args:
            category_id: The unique identifier of the category to be retrieved
            components: Indicates if the category components for the category should be returned in the response

        Returns:
            API response containing the category

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid category ID format or invalid components parameter
                - 401: Unauthorized - Authentication failed or insufficient permissions
                - 404: Not Found - Category with the specified ID does not exist
        """
        try:
            endpoint = f"categories/{category_id}"
            if components is not None:
                endpoint += f"?components={str(components).lower()}"

            api_response = self.feapi_request.make_get_request(endpoint)
            return api_response
        except ValueError as e:
            self._handle_api_error(e, endpoint, "GET")

    def DeleteCategory(self, category_id):
        """
        Delete Category by ID

        Args:
            category_id: The ID of the category

        Returns:
            API response containing dependency information

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid category ID format or category has dependencies
                - 401: Unauthorized - Authentication failed or insufficient permissions to delete categories
                - 404: Not Found - Category with the specified ID does not exist
        """
        try:
            api_response = self.feapi_request.make_delete_request(f"categories/{category_id}")
            return api_response
        except ValueError as e:
            self._handle_api_error(e, f"categories/{category_id}", "DELETE")

    def GetCategoryDependencies(self, category_id):
        """
        Gets the dependencies of a category

        Args:
            category_id: The unique identifier of the category

        Returns:
            API response containing the category dependencies

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid category ID format
                - 401: Unauthorized - Authentication failed or insufficient permissions
                - 404: Not Found - Category with the specified ID does not exist
        """
        try:
            api_response = self.feapi_request.make_get_request(f"categories/{category_id}/dependencies")
            return api_response
        except ValueError as e:
            self._handle_api_error(e, f"categories/{category_id}/dependencies", "GET")

    def GetCategoriesGeneration(self):
        """
        Retrieves a list of all categories and the order in which they will be installed on a customer's system

        Returns:
            API response containing the categories generation order

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid request format
                - 401: Unauthorized - Authentication failed or insufficient permissions
        """
        try:
            api_response = self.feapi_request.make_get_request("categories/generation")
            return api_response
        except ValueError as e:
            self._handle_api_error(e, "categories/generation", "GET")

    def GetContactCountCategories(self, request_data):
        """
        Get the Call Counts Trend Over Time Filtered by Categories [DEPRECATED]

        Args:
            request_data: The request data for the contact count report

        Returns:
            API response containing the contact count data

        Raises:
            FEAPIException: If the API call fails with detailed error information
                - 400: Bad Request - Invalid request data, missing parameters, or invalid date ranges
                - 401: Unauthorized - Authentication failed or insufficient permissions
        """
        try:
            api_response = self.feapi_request.make_post_request("report/contactcount/categories", request_data)
            return api_response
        except ValueError as e:
            self._handle_api_error(e, "report/contactcount/categories", "POST")


if __name__ == '__main__':
    
    # ------------------------ body ------------------------
    # Determine API and Security URLs
    my_api = 'https://feapidevatl.callminer.net/api/v2/'
    my_sec = 'https://sapidevatl.callminer.net/'
    username = os.getenv("FEAPI_username")
    password = os.getenv("FEAPI_pwd")
    tenant = "devatl"

    # call API
    endpoints = FEAPI_Endpoints(my_api, my_sec, username, password, tenant)

    # Test ValidateUserEntry with exception handling
    try:
        api_response = endpoints.ValidateUserEntry("hello")
        print(f"ValidateUserEntry api_response = {api_response}")
        api_response = endpoints.ValidateUserEntry("\"hello world\"")
        print(f"ValidateUserEntry api_response = {api_response}")
    except FEAPIException as e:
        print(f"ValidateUserEntry error: {e}")

    # Test Categories endpoints with exception handling
    print("\n--- Testing Categories Endpoints ---")

    # Get all categories
    try:
        categories_response = endpoints.GetAllCategories()
        print(f"GetAllCategories api_response = {categories_response}")
    except FEAPIException as e:
        print(f"GetAllCategories error: {e}")

    # Get all public categories only
    try:
        public_categories_response = endpoints.GetAllCategories(public_only=True)
        print(f"GetAllCategories (public only) api_response = {public_categories_response}")
    except FEAPIException as e:
        print(f"GetAllCategories (public only) error: {e}")

    # Get retro max days
    try:
        retro_max_days_response = endpoints.GetRetroMaxDays()
        print(f"GetRetroMaxDays api_response = {retro_max_days_response}")
    except FEAPIException as e:
        print(f"GetRetroMaxDays error: {e}")

    # Get categories generation order
    try:
        categories_generation_response = endpoints.GetCategoriesGeneration()
        print(f"GetCategoriesGeneration api_response = {categories_generation_response}")
    except FEAPIException as e:
        print(f"GetCategoriesGeneration error: {e}")

    # Example: Get a specific category by ID with error handling
    try:
        # Uncomment and replace with actual ID
        # category_response = endpoints.GetCategoryByCategoryId(1)
        # print(f"GetCategoryByCategoryId api_response = {category_response}")
        pass
    except FEAPIException as e:
        print(f"GetCategoryByCategoryId error: {e}")

    # Example: Get category components by ID with error handling
    try:
        # Uncomment and replace with actual ID
        # components_response = endpoints.GetCategoryComponentsByCategoryId(1)
        # print(f"GetCategoryComponentsByCategoryId api_response = {components_response}")
        pass
    except FEAPIException as e:
        print(f"GetCategoryComponentsByCategoryId error: {e}")


