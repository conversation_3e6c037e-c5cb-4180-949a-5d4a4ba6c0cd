import requests
import os
import logging


from requests.auth import AuthBase


class CMAuth(AuthBase):
    """Attaches HTTP CM Authorization to the given Request object."""

    def __init__(self, token):
        self.token = token

    def __call__(self, r):
        r.headers['Authorization'] = 'JWT ' + self.token
        return r
    

# globals

from enum import Enum  
  
class ApiRequestType(Enum):  
    POST = 'POST'  
    GET = 'GET'  
    PUT = 'PUT'  
    DELETE = 'DELETE'  
    

class FEAPIRequest:
    def __init__(self, api_address, security_address, username, passw, tenant, token=''):
        self.api_root = api_address
        self.security_api = security_address
        self.user = username
        self.pw = passw
        self.tenant = tenant
        self.headers = {'content-type': 'application/json'}
        self.token = token

    def _get_token_API10(self):  # etc. See APIRequest.cs in AutoCorrelate
        '''
        Obtain a token using the security API with a username and password
        :return: JWT token
        '''
        
        token_uri = 'security/getToken'
        success = False
        payload = {'apikey': self.tenant, 'username': self.user, 'password': self.pw}
        params = {'authtypeid': '32'}
        r = requests.post(self.security_api + token_uri, data=payload, params=params)
        if r.status_code == 200:
            self.token = r.text.strip('"')
            success = True
        return success

    def UpdateTokenFromResponse(self, api_response):  
        """  
        Updates the token attribute from the API response if a new token is provided.  
    
        This method attempts to extract the 'auth-token-updated' header from the given  
        API response and updates the instance's token if it differs from the current token.  
    
        Parameters:  
        - api_response: The response object from an API call. It is expected to have a  
        'headers' attribute which is a dictionary containing HTTP headers.  
    
        Error Handling:  
        - If 'api_response' is None or doesn't have a 'headers' attribute, an AttributeError  
        is caught, and a message is printed indicating the issue.  
        - If the 'auth-token-updated' header is not present in the 'headers', a KeyError  
        is caught, and a message is printed indicating that the header is missing.  
        """  
        try:  
            # Attempt to retrieve the updated token from the headers  
            newtoken = api_response.headers['auth-token-updated']  
            
            # Check if the new token is different from the current token  
            if newtoken != self.token:  
                self.token = newtoken  
                
        except AttributeError:  
            # Handle the case where api_response is None or doesn't have headers  
            print("Invalid API response: 'api_response' is None or missing 'headers'")  
            
        except KeyError:  
            # Handle the case where the 'auth-token-updated' header is missing  
            print("Invalid API response: 'auth-token-updated' header is missing")  



        

    def make_post_request(self,url, data=None):  
        """Make a POST request to the specified URL with the given data."""  
        return self.make_api_request(ApiRequestType.POST, url, data)  
    
    def make_get_request(self,url, data=None):  
        """Make a GET request to the specified URL."""  
        return self.make_api_request(ApiRequestType.GET, url, data)  
    
    def make_put_request(self,url, data=None):  
        """Make a PUT request to the specified URL with the given data."""  
        return self.make_api_request(ApiRequestType.PUT, url, data)  
    
    def make_delete_request(self,url, data=None):  
        """Make a DELETE request to the specified URL."""  
        return self.make_api_request(ApiRequestType.DELETE, url, data)  
  

    def make_api_request(self,request_type, url, data=None):  
        '''
        Calls api to run a request.post of the supplied url with the supplied data
        If not valid, raises Value Error
        '''
        # obtain token
        if self.token != '':
            token_obtained = True
        else:
            token_obtained = self._get_token_API10()

        logging.info('Token obtained? {0}'.format(token_obtained))
        #print('Token obtained? {0}'.format(token_obtained))

        api_url = self.api_root+url

        if token_obtained:
            my_auth = CMAuth(self.token)

            #switch request type
            if request_type == ApiRequestType.POST:  
                api_response = requests.post(url=api_url,data=data,headers=self.headers, auth=my_auth)
            
            elif request_type == ApiRequestType.GET:  
               api_response = requests.get(url=api_url,data=data,headers=self.headers, auth=my_auth)
            
            elif request_type == ApiRequestType.PUT:  
                api_response = requests.put(url=api_url,data=data,headers=self.headers, auth=my_auth)
            
            elif request_type == ApiRequestType.DELETE:  
                api_response = requests.delete(url=api_url,data=data,headers=self.headers, auth=my_auth)
            
                      
            # Check for good response
            if api_response.status_code == 200:
                self.UpdateTokenFromResponse(api_response)

                return api_response.text
            
            #API failure
            else:
                raise ValueError('Error in api_response. Status code: ',
                                 str(api_response.status_code)+f" Message = {api_response.text}")
            
        #Bad token
        else:
            raise ValueError('Error: Could not get a token.')




if __name__ == '__main__':
    
# ------------------------ body ------------------------
    # Determine API and Security URLs
    #Dev api and security
    #my_api = 'https://feapidevatl.callminer.net/api/v2/'
    #my_sec = 'https://sapidevatl.callminer.net/'
    #tenant = "devatl"
    
    
    #production authentication and api
    #my_api = 'https://feapi.callminer.net/api/v2/'
    #my_sec = 'https://sapi.callminer.net/'
    #tenant = "cmbia"

    #training Eureka user authnetication
    my_api = 'https://feapi.callminer.net/api/v2/'
    my_sec = 'https://sapicm.callminer.net/'
    tenant = "training02"

    # retrieve username and pwd from USer Account environemtn variable
    username = os.getenv("FEAPI_username")
    password = os.getenv("FEAPI_pwd")
    
    

    # call API
    api_request = FEAPIRequest(my_api, my_sec, username, password, tenant)

    api_response = api_request.make_post_request("search/validateuserentry","\"\\\"test query\\\":1.5\"")

    print (f"api_response = {api_response}")


